"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { CustomChatMessage } from "./components/CustomChatMessage";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome-message",
      role: "assistant",
      content:
        "您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？",
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 复刻 Vercel AI SDK 的流式响应解析逻辑
  const parseStreamChunk = useCallback((chunk: string): string => {
    const lines = chunk.split("\n").filter((line) => line.trim());
    let content = "";

    for (const line of lines) {
      // 解析 Vercel AI SDK 格式: 0:"text content"
      if (line.startsWith("0:")) {
        try {
          // 更强大的正则表达式来提取引号内的内容，处理转义字符
          const match = line.match(/^0:"((?:[^"\\]|\\.)*)"/);
          if (match) {
            let rawContent = match[1];

            // 处理各种转义序列
            const decodedContent = rawContent
              // 处理 Unicode 转义序列 \uXXXX
              .replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
                return String.fromCharCode(parseInt(code, 16));
              })
              // 处理常见转义字符
              .replace(/\\n/g, "\n")
              .replace(/\\r/g, "\r")
              .replace(/\\t/g, "\t")
              .replace(/\\"/g, '"')
              .replace(/\\\\/g, "\\");

            content += decodedContent;
          }
        } catch (error) {
          console.warn("Failed to parse stream chunk:", line, error);
          // 如果解析失败，尝试简单提取
          const simpleMatch = line.substring(2).trim();
          if (simpleMatch.startsWith('"') && simpleMatch.endsWith('"')) {
            content += simpleMatch.slice(1, -1);
          }
        }
      }
      // 处理其他类型的数据 (1:, 2:, 3: 等)
      else if (line.match(/^\d+:/)) {
        // 可以在这里处理其他类型的流数据
        console.log("Other stream data:", line);
      }
    }

    return content;
  }, []);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim() || isLoading) return;

      const userMessage: Message = {
        id: Date.now().toString(),
        role: "user",
        content: content.trim(),
      };

      setMessages((prev) => [...prev, userMessage]);
      setInput("");
      setIsLoading(true);

      // 创建助手消息占位符
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "",
      };

      setMessages((prev) => [...prev, assistantMessage]);

      try {
        const response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: `chat-${Date.now()}`,
            messages: [...messages, userMessage].map((msg) => ({
              role: msg.role,
              content: msg.content,
            })),
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        if (!reader) throw new Error("No response body");

        let fullContent = "";
        const decoder = new TextDecoder();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const parsedContent = parseStreamChunk(chunk);

          if (parsedContent) {
            fullContent += parsedContent;

            // 实时更新助手消息内容
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessage.id
                  ? { ...msg, content: fullContent }
                  : msg
              )
            );
          }
        }
      } catch (error) {
        console.error("Error sending message:", error);

        // 更新助手消息为错误信息
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === assistantMessage.id
              ? { ...msg, content: "抱歉，发生了错误。请稍后再试。" }
              : msg
          )
        );
      } finally {
        setIsLoading(false);
      }
    },
    [messages, isLoading, parseStreamChunk]
  );

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      sendMessage(input);
    },
    [input, sendMessage]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        sendMessage(input);
      }
    },
    [input, sendMessage]
  );

  return (
    <ErrorBoundary>
      <div className="h-full flex flex-col bg-background">
        {/* Messages Area */}
        <div className="flex-1 overflow-auto p-4 space-y-4">
          {messages.map((message) => (
            <CustomChatMessage
              key={message.id}
              message={message}
              isLoading={
                isLoading &&
                message.role === "assistant" &&
                message === messages[messages.length - 1]
              }
            />
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="border-t bg-background p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的问题..."
              className="flex-1 min-h-[44px] max-h-32 px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none rounded-md"
              disabled={isLoading}
              rows={1}
            />
            <button
              type="submit"
              disabled={isLoading || !input.trim()}
              className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 rounded-md"
            >
              {isLoading ? "发送中..." : "发送"}
            </button>
          </form>
        </div>
      </div>
    </ErrorBoundary>
  );
}
