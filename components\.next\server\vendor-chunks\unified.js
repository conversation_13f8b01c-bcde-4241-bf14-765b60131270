"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified";
exports.ids = ["vendor-chunks/unified"];
exports.modules = {

/***/ "(ssr)/./node_modules/unified/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/unified/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unified: () => (/* binding */ unified)\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/bail/index.js\");\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/is-buffer/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\n\n\n\n\n\n\n\n// Expose a frozen processor.\nconst unified = base().freeze()\n\nconst own = {}.hasOwnProperty\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)()\n  /** @type {Processor['attachers']} */\n  const attachers = []\n  /** @type {Record<string, unknown>} */\n  let namespace = {}\n  /** @type {boolean|undefined} */\n  let frozen\n  let freezeIndex = -1\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data\n  processor.Parser = undefined\n  processor.Compiler = undefined\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  // @ts-expect-error: overloads are handled.\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  // @ts-expect-error: overloads are handled.\n  processor.run = run\n  processor.runSync = runSync\n  // @ts-expect-error: overloads are handled.\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base()\n    let index = -1\n\n    while (++index < attachers.length) {\n      destination.use(...attachers[index])\n    }\n\n    destination.data(extend__WEBPACK_IMPORTED_MODULE_1__(true, {}, namespace))\n\n    return destination\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options)\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Number.POSITIVE_INFINITY\n\n    return processor\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value\n          addPlugin(plugin, ...options)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index]\n          break\n        }\n      }\n\n      if (entry) {\n        if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(entry[1]) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value)) {\n          value = extend__WEBPACK_IMPORTED_MODULE_1__(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments])\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node)\n    processor.freeze()\n\n    if (!callback && typeof doc === 'function') {\n      callback = doc\n      doc = undefined\n    }\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done)\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc)\n\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error)\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file)\n\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result\n          } else {\n            file.result = result\n          }\n\n          done(error, file)\n        }\n      })\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n\n    const file = vfile(doc)\n\n    processor.process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    // type-coverage:ignore-next-line\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile(value)\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/index.js\n");

/***/ })

};
;