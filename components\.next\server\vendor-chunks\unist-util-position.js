"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-position";
exports.ids = ["vendor-chunks/unist-util-position"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-position/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/unist-util-position/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pointEnd: () => (/* binding */ pointEnd),\n/* harmony export */   pointStart: () => (/* binding */ pointStart),\n/* harmony export */   position: () => (/* binding */ position)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nconst pointStart = point('start')\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nconst pointEnd = point('end')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {NodeLike | Node | null | undefined} [node]\n *   Node.\n * @returns {Position}\n *   Position.\n */\nfunction position(node) {\n  return {start: pointStart(node), end: pointEnd(node)}\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'start' | 'end'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {NodeLike | Node | null | undefined} [node]\n   * @returns {Point}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    // To do: next major: don’t return points when invalid.\n    return {\n      // @ts-expect-error: in practice, null is allowed.\n      line: point.line || null,\n      // @ts-expect-error: in practice, null is allowed.\n      column: point.column || null,\n      // @ts-expect-error: in practice, null is allowed.\n      offset: point.offset > -1 ? point.offset : null\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC1wb3NpdGlvbi9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxhQUFhLDBCQUEwQjtBQUN2QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLHVCQUF1QjtBQUNwQzs7QUFFQTtBQUNBO0FBQ0EsY0FBYyxRQUFRO0FBQ3RCLGNBQWMsaUNBQWlDO0FBQy9DO0FBQ0E7QUFDQSxjQUFjLDhCQUE4QjtBQUM1QyxjQUFjLDhCQUE4QjtBQUM1QztBQUNBO0FBQ0EsY0FBYywyQkFBMkI7QUFDekMsY0FBYywyQkFBMkI7QUFDekMsY0FBYywyQkFBMkI7QUFDekM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPOztBQUVQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9DQUFvQztBQUMvQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxVQUFVO0FBQ1Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxpQkFBaUI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsb0NBQW9DO0FBQ2pELGVBQWU7QUFDZjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3B5bGxhbWFpbmRleC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy91bmlzdC11dGlsLXBvc2l0aW9uL2xpYi9pbmRleC5qcz9hMDY0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QnKS5Qb3NpdGlvbn0gUG9zaXRpb25cbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0JykuTm9kZX0gTm9kZVxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QnKS5Qb2ludH0gUG9pbnRcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIE5vZGVMaWtlXG4gKiBAcHJvcGVydHkge3N0cmluZ30gdHlwZVxuICogQHByb3BlcnR5IHtQb3NpdGlvbkxpa2UgfCBudWxsIHwgdW5kZWZpbmVkfSBbcG9zaXRpb25dXG4gKlxuICogQHR5cGVkZWYgUG9zaXRpb25MaWtlXG4gKiBAcHJvcGVydHkge1BvaW50TGlrZSB8IG51bGwgfCB1bmRlZmluZWR9IFtzdGFydF1cbiAqIEBwcm9wZXJ0eSB7UG9pbnRMaWtlIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2VuZF1cbiAqXG4gKiBAdHlwZWRlZiBQb2ludExpa2VcbiAqIEBwcm9wZXJ0eSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2xpbmVdXG4gKiBAcHJvcGVydHkge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFtjb2x1bW5dXG4gKiBAcHJvcGVydHkge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFtvZmZzZXRdXG4gKi9cblxuLyoqXG4gKiBHZXQgdGhlIHN0YXJ0aW5nIHBvaW50IG9mIGBub2RlYC5cbiAqXG4gKiBAcGFyYW0gbm9kZVxuICogICBOb2RlLlxuICogQHJldHVybnNcbiAqICAgUG9pbnQuXG4gKi9cbmV4cG9ydCBjb25zdCBwb2ludFN0YXJ0ID0gcG9pbnQoJ3N0YXJ0JylcblxuLyoqXG4gKiBHZXQgdGhlIGVuZGluZyBwb2ludCBvZiBgbm9kZWAuXG4gKlxuICogQHBhcmFtIG5vZGVcbiAqICAgTm9kZS5cbiAqIEByZXR1cm5zXG4gKiAgIFBvaW50LlxuICovXG5leHBvcnQgY29uc3QgcG9pbnRFbmQgPSBwb2ludCgnZW5kJylcblxuLyoqXG4gKiBHZXQgdGhlIHBvc2l0aW9uYWwgaW5mbyBvZiBgbm9kZWAuXG4gKlxuICogQHBhcmFtIHtOb2RlTGlrZSB8IE5vZGUgfCBudWxsIHwgdW5kZWZpbmVkfSBbbm9kZV1cbiAqICAgTm9kZS5cbiAqIEByZXR1cm5zIHtQb3NpdGlvbn1cbiAqICAgUG9zaXRpb24uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwb3NpdGlvbihub2RlKSB7XG4gIHJldHVybiB7c3RhcnQ6IHBvaW50U3RhcnQobm9kZSksIGVuZDogcG9pbnRFbmQobm9kZSl9XG59XG5cbi8qKlxuICogR2V0IHRoZSBwb3NpdGlvbmFsIGluZm8gb2YgYG5vZGVgLlxuICpcbiAqIEBwYXJhbSB7J3N0YXJ0JyB8ICdlbmQnfSB0eXBlXG4gKiAgIFNpZGUuXG4gKiBAcmV0dXJuc1xuICogICBHZXR0ZXIuXG4gKi9cbmZ1bmN0aW9uIHBvaW50KHR5cGUpIHtcbiAgcmV0dXJuIHBvaW50XG5cbiAgLyoqXG4gICAqIEdldCB0aGUgcG9pbnQgaW5mbyBvZiBgbm9kZWAgYXQgYSBib3VuZCBzaWRlLlxuICAgKlxuICAgKiBAcGFyYW0ge05vZGVMaWtlIHwgTm9kZSB8IG51bGwgfCB1bmRlZmluZWR9IFtub2RlXVxuICAgKiBAcmV0dXJucyB7UG9pbnR9XG4gICAqL1xuICBmdW5jdGlvbiBwb2ludChub2RlKSB7XG4gICAgY29uc3QgcG9pbnQgPSAobm9kZSAmJiBub2RlLnBvc2l0aW9uICYmIG5vZGUucG9zaXRpb25bdHlwZV0pIHx8IHt9XG5cbiAgICAvLyBUbyBkbzogbmV4dCBtYWpvcjogZG9u4oCZdCByZXR1cm4gcG9pbnRzIHdoZW4gaW52YWxpZC5cbiAgICByZXR1cm4ge1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogaW4gcHJhY3RpY2UsIG51bGwgaXMgYWxsb3dlZC5cbiAgICAgIGxpbmU6IHBvaW50LmxpbmUgfHwgbnVsbCxcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IGluIHByYWN0aWNlLCBudWxsIGlzIGFsbG93ZWQuXG4gICAgICBjb2x1bW46IHBvaW50LmNvbHVtbiB8fCBudWxsLFxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogaW4gcHJhY3RpY2UsIG51bGwgaXMgYWxsb3dlZC5cbiAgICAgIG9mZnNldDogcG9pbnQub2Zmc2V0ID4gLTEgPyBwb2ludC5vZmZzZXQgOiBudWxsXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-position/lib/index.js\n");

/***/ })

};
;