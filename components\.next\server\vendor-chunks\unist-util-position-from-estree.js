"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-position-from-estree";
exports.ids = ["vendor-chunks/unist-util-position-from-estree"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-position-from-estree/lib/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/unist-util-position-from-estree/lib/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   positionFromEstree: () => (/* binding */ positionFromEstree)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Point} UnistPoint\n * @typedef {import('unist').Position} UnistPosition\n */\n\n/**\n * @typedef {[start: number | null | undefined, end: number | null | undefined]} RangeLike\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n *\n * @typedef LocLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef NodeLike\n * @property {LocLike | null | undefined} [loc]\n * @property {RangeLike | null | undefined} [range]\n * @property {number | null | undefined} [start]\n * @property {number | null | undefined} [end]\n */\n\n/**\n * Turn an estree `node` into a unist `position`.\n *\n * @param {NodeLike | null | undefined} [node]\n *   estree node.\n * @returns {UnistPosition | undefined}\n *   unist position.\n */\nfunction positionFromEstree(node) {\n  const nodeLike = node || {}\n  const loc = nodeLike.loc || {}\n  const range = nodeLike.range || [undefined, undefined]\n  const start = pointOrUndefined(loc.start, range[0] || nodeLike.start)\n  const end = pointOrUndefined(loc.end, range[1] || nodeLike.end)\n\n  if (start && end) {\n    return {start, end}\n  }\n}\n\n/**\n * @param {unknown} estreePoint\n *   estree point.\n * @param {unknown} estreeOffset\n *  estree offset.\n * @returns {UnistPoint | undefined}\n *   unist point.\n */\nfunction pointOrUndefined(estreePoint, estreeOffset) {\n  if (estreePoint && typeof estreePoint === 'object') {\n    const line =\n      'line' in estreePoint ? numberOrUndefined(estreePoint.line) : undefined\n    const column =\n      'column' in estreePoint\n        ? numberOrUndefined(estreePoint.column)\n        : undefined\n\n    if (line && column !== undefined) {\n      return {\n        line,\n        column: column + 1,\n        offset: numberOrUndefined(estreeOffset)\n      }\n    }\n  }\n}\n\n/**\n * @param {unknown} value\n * @returns {number | undefined}\n */\nfunction numberOrUndefined(value) {\n  return typeof value === 'number' && value > -1 ? value : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-position-from-estree/lib/index.js\n");

/***/ })

};
;